<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تهنئة بزواج النورين - Wedding Congratulations</title>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Scheherazade+New:wght@400;700&family=Noto+Sans+Arabic:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Amiri', 'Scheherazade New', 'Noto Sans Arabic', serif;
            background: linear-gradient(135deg, #f8f4e6 0%, #e8dcc0 50%, #d4c5a0 100%);
            height: 100vh;
            direction: rtl;
            text-align: center;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            max-width: 900px;
            width: 90%;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
        }

        .islamic-pattern {
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 200px;
            height: 60px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 60"><defs><pattern id="islamic" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse"><path d="M20,0 L40,20 L20,40 L0,20 Z" fill="none" stroke="%23d4af37" stroke-width="2"/></pattern></defs><rect width="200" height="60" fill="url(%23islamic)"/></svg>') repeat;
            opacity: 0.3;
        }

        .main-title {
            font-size: 2.5rem;
            color: #d4af37;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            margin: 20px 0 15px 0;
            font-weight: 700;
            animation: fadeInDown 1s ease-out;
        }

        .subtitle {
            font-size: 1.3rem;
            color: #8b4513;
            margin-bottom: 20px;
            font-weight: 600;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .wedding-card {
            background: linear-gradient(145deg, #ffffff 0%, #faf8f3 100%);
            border-radius: 15px;
            padding: 25px;
            margin: 15px 0;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border: 2px solid #d4af37;
            position: relative;
            overflow: hidden;
            animation: slideInRight 1s ease-out 0.6s both;
        }

        .wedding-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(212,175,55,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        .wedding-card-content {
            position: relative;
            z-index: 2;
        }

        .congratulations-text {
            font-size: 1.8rem;
            color: #2c5530;
            text-align: center;
            margin-bottom: 15px;
            line-height: 1.6;
            font-weight: 600;
        }

        .quranic-verse {
            background: linear-gradient(135deg, #f0f8f0 0%, #e8f5e8 100%);
            border-right: 4px solid #d4af37;
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-size: 1.2rem;
            line-height: 1.8;
            color: #1a4d1a;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .verse-reference {
            font-size: 1rem;
            color: #8b4513;
            margin-top: 10px;
            font-style: italic;
        }

        .blessing-section {
            background: linear-gradient(135deg, #fff8dc 0%, #f5f5dc 100%);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            border: 2px solid #daa520;
            text-align: center;
        }

        .blessing-title {
            font-size: 1.3rem;
            color: #8b4513;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .blessing-text {
            font-size: 1.1rem;
            color: #2c5530;
            line-height: 1.5;
            margin-bottom: 8px;
        }

        .dua-section {
            background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            border: 2px solid #4682b4;
            text-align: center;
        }

        .hearts-decoration {
            font-size: 1.5rem;
            color: #d4af37;
            margin: 10px 0;
            text-align: center;
            animation: pulse 2s infinite;
        }

        .footer {
            text-align: center;
            margin-top: 15px;
            padding: 10px;
            background: rgba(212,175,55,0.1);
            border-radius: 8px;
        }

        .brand {
            font-size: 0.9rem;
            color: #8b4513;
            font-weight: 600;
        }

        .share-buttons {
            margin: 15px 0;
            text-align: center;
        }

        .share-btn {
            display: inline-block;
            padding: 8px 15px;
            margin: 0 5px;
            background: linear-gradient(135deg, #d4af37 0%, #b8941f 100%);
            color: white;
            text-decoration: none;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
        }

        .share-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(100px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .main-title { font-size: 2rem; }
            .subtitle { font-size: 1.1rem; }
            .congratulations-text { font-size: 1.5rem; }
            .quranic-verse { font-size: 1rem; }
            .wedding-card { padding: 20px; }
            .container { padding: 15px; width: 95%; }
            .blessing-text { font-size: 1rem; }
        }

        @media (max-width: 480px) {
            .main-title { font-size: 1.8rem; margin: 10px 0; }
            .subtitle { font-size: 1rem; margin-bottom: 15px; }
            .congratulations-text { font-size: 1.3rem; }
            .quranic-verse { font-size: 0.9rem; padding: 12px; }
            .share-btn { padding: 6px 12px; margin: 2px; font-size: 0.8rem; }
            .blessing-text { font-size: 0.9rem; }
            .wedding-card { padding: 15px; margin: 10px 0; }
        }

        @media print {
            body {
                background: white !important;
                color: black !important;
            }
            .share-buttons {
                display: none !important;
            }
            .wedding-card {
                box-shadow: none !important;
                border: 2px solid #d4af37 !important;
                page-break-inside: avoid;
            }
            .main-title {
                color: #8b4513 !important;
                text-shadow: none !important;
            }
            .quranic-verse {
                background: #f5f5f5 !important;
                border: 1px solid #ccc !important;
            }
            .blessing-section, .dua-section {
                background: #f9f9f9 !important;
                border: 1px solid #ddd !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="islamic-pattern"></div>
            <h1 class="main-title">تهنئة بزواج النورين</h1>
            <h2 class="subtitle">Wedding Congratulations for the Two Lights</h2>
        </header>

        <main>
            <div class="wedding-card">
                <div class="wedding-card-content">
                    <div class="congratulations-text">
                        بارك الله لكما وبارك عليكما وجمع بينكما في خير
                    </div>

                    <div class="hearts-decoration">
                        💖 ✨ 🤲 ✨ 💖
                    </div>

                    <div class="quranic-verse">
                        "وَمِنْ آيَاتِهِ أَنْ خَلَقَ لَكُم مِّنْ أَنفُسِكُمْ أَزْوَاجًا لِّتَسْكُنُوا إِلَيْهَا وَجَعَلَ بَيْنَكُم مَّوَدَّةً وَرَحْمَةً"
                        <div class="verse-reference">سورة الروم - آية 21</div>
                    </div>

                    <div class="blessing-text">
                        نتقدم بأحر التهاني للعالم الإسلامي بمناسبة زواج النورين المبارك
                    </div>
                    <div class="blessing-text">
                        اللهم بارك لهما وبارك عليهما وألف بين قلبيهما في خير
                    </div>
                    <div class="blessing-text">
                        💝 المودة والرحمة • 🤲 الدعاء والذكر • 🕌 البيت المسلم السعيد
                    </div>
                </div>
            </div>

            <div class="share-buttons">
                <a href="#" class="share-btn" onclick="shareOnFacebook()">مشاركة</a>
                <a href="#" class="share-btn" onclick="shareOnWhatsApp()">واتساب</a>
                <a href="#" class="share-btn" onclick="downloadAsImage()">تحميل</a>
                <a href="#" class="share-btn" onclick="printCard()">طباعة</a>
            </div>
        </main>

        <footer class="footer">
            <div class="brand">
                تصميم AliToucan - مع أطيب التمنيات للعالم الإسلامي
            </div>
        </footer>
    </div>

    <script>
        function shareOnFacebook() {
            const url = encodeURIComponent(window.location.href);
            const text = encodeURIComponent('تهنئة بزواج النورين - بارك الله لكما وبارك عليكما');
            window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}&quote=${text}`, '_blank');
        }

        function shareOnTwitter() {
            const url = encodeURIComponent(window.location.href);
            const text = encodeURIComponent('تهنئة بزواج النورين - بارك الله لكما وبارك عليكما #زواج_مبارك #التهاني');
            window.open(`https://twitter.com/intent/tweet?url=${url}&text=${text}`, '_blank');
        }

        function shareOnWhatsApp() {
            const text = encodeURIComponent('تهنئة بزواج النورين - بارك الله لكما وبارك عليكما\n\n' + window.location.href);
            window.open(`https://wa.me/?text=${text}`, '_blank');
        }

        function downloadAsImage() {
            // Create a canvas to capture the content
            html2canvas(document.querySelector('.container'), {
                useCORS: true,
                allowTaint: true,
                scale: 2,
                backgroundColor: '#f8f4e6'
            }).then(canvas => {
                // Create download link
                const link = document.createElement('a');
                link.download = 'تهنئة_زواج_النورين.png';
                link.href = canvas.toDataURL();
                link.click();
            }).catch(error => {
                console.error('Error generating image:', error);
                alert('عذراً، حدث خطأ في تحميل الصورة. يرجى المحاولة مرة أخرى.');
            });
        }

        function printCard() {
            window.print();
        }

        // Add smooth scrolling animation on load
        window.addEventListener('load', function() {
            document.body.style.opacity = '0';
            document.body.style.transition = 'opacity 0.5s ease-in';
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 100);

            // Load html2canvas library for image download functionality
            if (!window.html2canvas) {
                const script = document.createElement('script');
                script.src = 'https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js';
                document.head.appendChild(script);
            }
        });

        // Add floating animation to hearts
        document.addEventListener('DOMContentLoaded', function() {
            const hearts = document.querySelectorAll('.hearts-decoration');
            hearts.forEach(heart => {
                heart.addEventListener('mouseover', function() {
                    this.style.transform = 'scale(1.2) rotate(5deg)';
                    this.style.transition = 'transform 0.3s ease';
                });
                heart.addEventListener('mouseout', function() {
                    this.style.transform = 'scale(1) rotate(0deg)';
                });
            });
        });
    </script>
</body>
</html>
