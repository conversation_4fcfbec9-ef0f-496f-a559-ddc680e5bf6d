<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تهنئة بزواج الإمام علي والزهراء عليهما السلام</title>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Scheherazade+New:wght@400;700&family=Noto+Sans+Arabic:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Amiri', 'Scheherazade New', 'Noto Sans Arabic', serif;
            background: linear-gradient(135deg, #f8f4e6 0%, #e8dcc0 50%, #d4c5a0 100%);
            height: 100vh;
            direction: rtl;
            text-align: center;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            max-width: 900px;
            width: 90%;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
        }

        .islamic-pattern {
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 200px;
            height: 60px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 60"><defs><pattern id="islamic" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse"><path d="M20,0 L40,20 L20,40 L0,20 Z" fill="none" stroke="%23d4af37" stroke-width="2"/></pattern></defs><rect width="200" height="60" fill="url(%23islamic)"/></svg>') repeat;
            opacity: 0.3;
        }

        .main-title {
            font-size: 2.5rem;
            color: #d4af37;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            margin: 20px 0 15px 0;
            font-weight: 700;
            animation: fadeInDown 1s ease-out;
        }

        .subtitle {
            font-size: 1.3rem;
            color: #8b4513;
            margin-bottom: 20px;
            font-weight: 600;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .wedding-card {
            background: linear-gradient(145deg, #ffffff 0%, #faf8f3 100%);
            border-radius: 15px;
            padding: 25px;
            margin: 15px 0;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border: 2px solid #d4af37;
            position: relative;
            overflow: hidden;
            animation: slideInRight 1s ease-out 0.6s both;
        }

        .wedding-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(212,175,55,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        .wedding-card-content {
            position: relative;
            z-index: 2;
        }

        .congratulations-text {
            font-size: 1.8rem;
            color: #2c5530;
            text-align: center;
            margin-bottom: 15px;
            line-height: 1.6;
            font-weight: 600;
        }

        .quranic-verse {
            background: linear-gradient(135deg, #f0f8f0 0%, #e8f5e8 100%);
            border-right: 4px solid #d4af37;
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-size: 1.2rem;
            line-height: 1.8;
            color: #1a4d1a;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .verse-reference {
            font-size: 1rem;
            color: #8b4513;
            margin-top: 10px;
            font-style: italic;
        }

        .blessing-section {
            background: linear-gradient(135deg, #fff8dc 0%, #f5f5dc 100%);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            border: 2px solid #daa520;
            text-align: center;
        }

        .blessing-title {
            font-size: 1.3rem;
            color: #8b4513;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .blessing-text {
            font-size: 1.1rem;
            color: #2c5530;
            line-height: 1.5;
            margin-bottom: 8px;
        }

        .dua-section {
            background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            border: 2px solid #4682b4;
            text-align: center;
        }

        .hearts-decoration {
            font-size: 1.5rem;
            color: #d4af37;
            margin: 10px 0;
            text-align: center;
            animation: pulse 2s infinite;
        }

        .footer {
            text-align: center;
            margin-top: 15px;
            padding: 10px;
            background: rgba(212,175,55,0.1);
            border-radius: 8px;
        }

        .brand {
            font-size: 0.9rem;
            color: #8b4513;
            font-weight: 600;
        }

        .share-buttons {
            margin: 15px 0;
            text-align: center;
        }

        .share-btn {
            display: inline-block;
            padding: 8px 15px;
            margin: 0 5px;
            background: linear-gradient(135deg, #d4af37 0%, #b8941f 100%);
            color: white;
            text-decoration: none;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
        }

        .share-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(100px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }

        @media (max-width: 768px) {
            .main-title { font-size: 2rem; }
            .subtitle { font-size: 1.1rem; }
            .congratulations-text { font-size: 1.5rem; }
            .quranic-verse { font-size: 1rem; }
            .wedding-card { padding: 20px; }
            .container { padding: 15px; width: 95%; }
            .blessing-text { font-size: 1rem; }
        }

        @media (max-width: 480px) {
            .main-title { font-size: 1.8rem; margin: 10px 0; }
            .subtitle { font-size: 1rem; margin-bottom: 15px; }
            .congratulations-text { font-size: 1.3rem; }
            .quranic-verse { font-size: 0.9rem; padding: 12px; }
            .share-btn { padding: 6px 12px; margin: 2px; font-size: 0.8rem; }
            .blessing-text { font-size: 0.9rem; }
            .wedding-card { padding: 15px; margin: 10px 0; }
        }

        @media print {
            body {
                background: white !important;
                color: black !important;
            }
            .share-buttons {
                display: none !important;
            }
            .wedding-card {
                box-shadow: none !important;
                border: 2px solid #d4af37 !important;
                page-break-inside: avoid;
            }
            .main-title {
                color: #8b4513 !important;
                text-shadow: none !important;
            }
            .quranic-verse {
                background: #f5f5f5 !important;
                border: 1px solid #ccc !important;
            }
            .blessing-section, .dua-section {
                background: #f9f9f9 !important;
                border: 1px solid #ddd !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="islamic-pattern"></div>
            <h1 class="main-title">تهنئة بزواج الإمام علي والزهراء</h1>
            <h2 class="subtitle">عليهما أفضل الصلاة والسلام</h2>
        </header>

        <main>
            <div class="wedding-card">
                <div class="wedding-card-content">
                    <div class="congratulations-text">
                        بارك الله في زواج أمير المؤمنين علي وسيدة نساء العالمين فاطمة الزهراء
                    </div>

                    <div class="hearts-decoration">
                        🌹 ✨ 🤲 ✨ 🌹
                    </div>

                    <div class="quranic-verse">
                        "وَمِنْ آيَاتِهِ أَنْ خَلَقَ لَكُم مِّنْ أَنفُسِكُمْ أَزْوَاجًا لِّتَسْكُنُوا إِلَيْهَا وَجَعَلَ بَيْنَكُم مَّوَدَّةً وَرَحْمَةً"
                        <div class="verse-reference">سورة الروم - آية 21</div>
                    </div>

                    <div class="blessing-text">
                        زواج مبارك جمع بين نور الإمامة ونور النبوة
                    </div>
                    <div class="blessing-text">
                        علي المرتضى وفاطمة البتول عليهما السلام
                    </div>
                    <div class="blessing-text">
                        🌹 أهل البيت عليهم السلام • ⭐ النور المحمدي • 🕌 البيت الطاهر
                    </div>
                </div>
            </div>

            <div class="share-buttons">
                <a href="#" class="share-btn" onclick="shareOnFacebook()">مشاركة</a>
                <a href="#" class="share-btn" onclick="shareOnWhatsApp()">واتساب</a>
                <a href="#" class="share-btn" onclick="downloadAsImage()">تحميل</a>
                <a href="#" class="share-btn" onclick="printCard()">طباعة</a>
            </div>
        </main>

        <footer class="footer">
            <div class="brand">
                تصميم AliToucan - تكريماً لأهل البيت عليهم السلام
            </div>
        </footer>
    </div>

    <script>
        function shareOnFacebook() {
            const url = encodeURIComponent(window.location.href);
            const text = encodeURIComponent('تهنئة بزواج الإمام علي والزهراء عليهما السلام - زواج مبارك جمع بين نور الإمامة ونور النبوة');
            window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}&quote=${text}`, '_blank');
        }

        function shareOnTwitter() {
            const url = encodeURIComponent(window.location.href);
            const text = encodeURIComponent('تهنئة بزواج الإمام علي والزهراء عليهما السلام #أهل_البيت #الإمام_علي #فاطمة_الزهراء');
            window.open(`https://twitter.com/intent/tweet?url=${url}&text=${text}`, '_blank');
        }

        function shareOnWhatsApp() {
            const text = encodeURIComponent('تهنئة بزواج الإمام علي والزهراء عليهما السلام\n\n' + window.location.href);
            window.open(`https://wa.me/?text=${text}`, '_blank');
        }

        function downloadAsImage() {
            // Show loading indicator
            const downloadBtn = document.querySelector('[onclick="downloadAsImage()"]');
            const originalText = downloadBtn.textContent;
            downloadBtn.textContent = 'جاري التحميل...';
            downloadBtn.style.opacity = '0.7';
            downloadBtn.style.pointerEvents = 'none';

            // Hide share buttons temporarily for cleaner image
            const shareButtons = document.querySelector('.share-buttons');
            shareButtons.style.display = 'none';

            // Create a canvas to capture the content with enhanced settings
            html2canvas(document.querySelector('.container'), {
                useCORS: true,
                allowTaint: true,
                scale: 3, // Higher resolution
                backgroundColor: '#f8f4e6',
                width: 900,
                height: 600,
                scrollX: 0,
                scrollY: 0,
                foreignObjectRendering: true,
                logging: false,
                imageTimeout: 15000,
                removeContainer: true
            }).then(canvas => {
                // Restore share buttons
                shareButtons.style.display = 'block';

                // Create download link with Arabic filename
                const link = document.createElement('a');
                link.download = 'تهنئة_زواج_الإمام_علي_والزهراء_عليهما_السلام.png';

                // Convert to high quality image
                const imageData = canvas.toDataURL('image/png', 1.0);
                link.href = imageData;

                // Trigger download
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Reset button
                downloadBtn.textContent = originalText;
                downloadBtn.style.opacity = '1';
                downloadBtn.style.pointerEvents = 'auto';

                // Show success message
                showMessage('تم تحميل الصورة بنجاح! 🎉', 'success');

            }).catch(error => {
                console.error('Error generating image:', error);

                // Restore elements
                shareButtons.style.display = 'block';
                downloadBtn.textContent = originalText;
                downloadBtn.style.opacity = '1';
                downloadBtn.style.pointerEvents = 'auto';

                showMessage('عذراً، حدث خطأ في تحميل الصورة. يرجى المحاولة مرة أخرى.', 'error');
            });
        }

        function showMessage(message, type) {
            // Create message element
            const messageDiv = document.createElement('div');
            messageDiv.textContent = message;
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 25px;
                border-radius: 10px;
                color: white;
                font-weight: bold;
                z-index: 1000;
                animation: slideIn 0.3s ease-out;
                background: ${type === 'success' ? '#4CAF50' : '#f44336'};
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            `;

            document.body.appendChild(messageDiv);

            // Remove after 3 seconds
            setTimeout(() => {
                messageDiv.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        document.body.removeChild(messageDiv);
                    }
                }, 300);
            }, 3000);
        }

        function printCard() {
            window.print();
        }

        // Add smooth scrolling animation on load
        window.addEventListener('load', function() {
            document.body.style.opacity = '0';
            document.body.style.transition = 'opacity 0.5s ease-in';
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 100);

            // Load html2canvas library for image download functionality
            if (!window.html2canvas) {
                const script = document.createElement('script');
                script.src = 'https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js';
                document.head.appendChild(script);
            }
        });

        // Add floating animation to hearts
        document.addEventListener('DOMContentLoaded', function() {
            const hearts = document.querySelectorAll('.hearts-decoration');
            hearts.forEach(heart => {
                heart.addEventListener('mouseover', function() {
                    this.style.transform = 'scale(1.2) rotate(5deg)';
                    this.style.transition = 'transform 0.3s ease';
                });
                heart.addEventListener('mouseout', function() {
                    this.style.transform = 'scale(1) rotate(0deg)';
                });
            });
        });
    </script>
</body>
</html>
